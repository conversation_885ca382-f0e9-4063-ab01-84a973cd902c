<template>
  <div class="salary-tax2-view">
    <!-- Compact header -->
    <div class="compact-header">
      <div class="header-content">
        <h1 class="page-title">
          <span class="title-icon">💼</span>
          薪酬个税管理系统
        </h1>
      </div>
      <div class="header-controls">
        <select v-model="selectedYear" class="year-select">
          <option value="">请选择年份</option>
          <option value="2024">2024年</option>
          <option value="2025">2025年</option>
          <option value="2026">2026年</option>
          <option value="2027">2027年</option>
        </select>
        <button @click="fetchAllData" class="fetch-button" :disabled="!selectedYear || isLoading">
          <span class="button-icon">📥</span>
          <span>拉取数据</span>
        </button>
        <button @click="saveAllData" class="save-button" :disabled="isLoading">
          <span class="button-icon">💾</span>
          <span>保存数据</span>
        </button>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="action-buttons">
      <button @click="matchIdAndProject" class="action-button match">
        <span class="button-icon">🔗</span>
        <span>匹配身份证号及项目</span>
      </button>
      <button @click="calculateTax" class="action-button tax">
        <span class="button-icon">🧮</span>
        <span>计算个税</span>
      </button>
      <button @click="generateDeclaration" class="action-button declaration">
        <span class="button-icon">📄</span>
        <span>生成申报表</span>
      </button>
      <button @click="generateDivision" class="action-button declaration">
        <span class="button-icon">📑</span>
        <span>生成劳务派遣分割表</span>
      </button>
      <button @click="pushDeclaration" class="action-button push">
        <span class="button-icon">🚀</span>
        <span>构建财务一体化计提发放模板(薪酬发放表)</span>
      </button>
      <button @click="buildFinanceTemplate" class="action-button template">
        <span class="button-icon">🏗️</span>
        <span>构建财务一体化计提发放模板(社保公积金表)</span>
      </button>
      <button @click="calculateSocialSecurityAllocation" class="action-button export">
        <span class="button-icon">📤</span>
        <span>计算社保公积金回摊</span>
      </button>
      <button @click="exportAllTablesToExcel" class="action-button export-all">
        <span class="button-icon">💾</span>
        <span>导出所有表格</span>
      </button>
      <button @click="triggerSpecialDeductionImport" class="action-button import">
        <span class="button-icon">📥</span>
        <span>导入专项附加</span>
      </button>
      <input ref="fileInput" type="file" accept=".xlsx,.xls" style="display: none"
        @change="handleSpecialDeductionImport" />
    </div>

    <!-- Loading indicator -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">处理中...</div>
    </div>

    <!-- Univer container -->
    <div class="univer-container" :class="{ 'loading': isLoading }">
      <div ref="univerContainer" class="univer-spreadsheet"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { createUniver, defaultTheme, LocaleType, merge, Univer, FUniver } from '@univerjs/presets'
import { UniverSheetsCorePreset } from '@univerjs/presets/preset-sheets-core'
import UniverPresetSheetsCoreZhCN from '@univerjs/presets/preset-sheets-core/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-core.css'

import { UniverSheetsConditionalFormattingPreset } from '@univerjs/presets/preset-sheets-conditional-formatting'
import sheetsConditionalFormattingZhCN from '@univerjs/presets/preset-sheets-conditional-formatting/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-conditional-formatting.css'

import { UniverSheetsDataValidationPreset } from '@univerjs/presets/preset-sheets-data-validation'
import sheetsDataValidationZhCN from '@univerjs/presets/preset-sheets-data-validation/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-data-validation.css'

import { UniverSheetsFilterPreset } from '@univerjs/presets/preset-sheets-filter'
import sheetsFilterZhCN from '@univerjs/presets/preset-sheets-filter/locales/zh-CN'
import '@univerjs/presets/lib/styles/preset-sheets-filter.css'

import * as ExcelJS from 'exceljs'
import taxCalculatorTs from '../scripts/taxCalculator'
// 响应式数据
const univerContainer = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
let univerInstance: Univer | null = null
let univerAPIInstance: FUniver | null = null
const isLoading = ref(false)
const activeTabIndex = ref(0)
const undoStack = ref([])
const lastUpdateTime = ref('')
const selectedYear = ref('')

// 标签页配置
const tabs = ref([
  { name: '个税申报表', key: 'taxDeclaration' },
  { name: '算税底稿', key: 'taxCalculation' },
  { name: '包干费', key: 'subsidyPackage' },
  { name: '一次性年终奖', key: 'bonus' },
  { name: '外包薪酬表', key: 'outsourcingSalary' },
  { name: '社保公积金实际缴纳表', key: 'socialSecurityAndHousingFund' },
  { name: '薪酬总表(无一次性年终及包干)', key: 'socialSecurityAndHousingFundAdjustment' },
  { name: '累计专项附加扣除表', key: 'specialDeduction' },
  { name: '异地纳税', key: 'remoteTax' },
  { name: '身份证号表', key: 'idCard' },
  { name: '项目匹配表', key: 'projectMapping' }
])

// 默认初始化数据
const initializeDefaultData = () => {
  return {
    taxDeclaration: [
      [
        '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
        '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
        '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
        '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
        '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
        '准予扣除的捐赠额', '减免税额', '备注'
      ],
      [
        'E001', '张三', '身份证', '110101199001011234', 15000, 0,
        800, 240, 80, 960, 1000, 0, 1000, 0, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
      ],
      [
        'E002', '李四', '身份证', '110101199102022345', 12000, 0,
        750, 200, 60, 900, 1000, 400, 0, 1500, 2000, 0, 0, 0, 0, 0, 0, 0, 0, ''
      ]
    ],
    taxCalculation: [
      [
        '月份', '算税地区', '社保缴纳单位', '公积金缴纳单位', '年金缴纳单位', '身份证号', '姓名', '成本所属项目', '财务标准项目', '财务标准单位', '薪酬类别',
        '本期收入', '基本养老保险费', '住房公积金', '基本医疗保险费',
        '失业保险费', '企业(职业)年金', '其它扣款', '调整收入', '调整扣除',
        '调整累计个税', '累计社保', '累计专项附加', '累计法定扣除',
        '累计调整扣除', '累计收入', '累计扣除', '累计应扣税款',
        '累计上次税款', '本次税款', '本次达到税率', '一次性年终奖校验', '备注'
      ],
      [
        1, '北京市(注意旁边月份为整数)', '华南', '武汉1', '美国', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '财务一体化单位1', '工资',
        15000, 800, 960, 240, 80, 0, 0, 0, 0, 0, 2080, 4000, 5000, 0,
        15000, 11080, 595, 0, 595, '10%', '否', '备注1'
      ]
    ],
    specialDeduction: [
      [
        '工号', '姓名', '证件类型', '证件号码', '所得期间起', '所得期间止',
        '本期收入', '累计子女教育', '累计继续教育', '累计住房贷款利息',
        '累计住房租金', '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
      ],
      [
        'E001', '张三', '身份证', '110101199001011234', '2025-01-01', '2025-01-31',
        15000, 1000, 0, 1000, 0, 2000, 0, 0
      ]
    ],
    remoteTax: [
      [
        '预留', '身份证号', '姓名', '一月', '二月', '三月', '四月', '五月', '六月',
        '七月', '八月', '九月', '十月', '十一月', '十二月'
      ],
      [
        '', '110101199001011234', '张三', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
      ]
    ],
    idCard: [
      ['姓名', '身份证号', '银行卡号', '开户行名称', '开户行编码', '备注'],
      ['张三', '110101199001011234', '6217000010000000000', '中国银行', '9999999999999999999', '备注1'],
      ['李四', '110101199102022345', '6217000020000000000', '中国银行', '9999999999999999999', '备注2']
    ],
    projectMapping: [
      ['人资项目名称', '标准项目名称', '标准项目编码', '财务标准单位', '财务标准单位编码'],
      ['人资项目1', '财务一体化项目1', '12211235664', '财务一体化单位1', '财务一体化单位编码1']
    ],
    subsidyPackage: [
      ['月份', '身份证号', '姓名', '成本所属项目', '财务标准项目', '工作餐补贴', '交通补贴', '通讯补贴', '住房补贴', '取证补贴', '其他补贴', '合计补贴', '备注'],
      [1, '110101199001011234', '张三', '研发项目A', '财务一体化项目1', 15000, 800, 960, 240, 80, 80, 80, "备注1"]
    ],
    bonus: [
      ['缴纳月份', '算税地区', '身份证号', '姓名', '成本所属项目', '财务标准项目', '薪酬类别', '原奖金金额', '报税收入', '税额', '税率', '备注'],
      [1, '北京市', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '商务兑现', 15000, 15000, 595, '10%', '备注1']
    ],
    outsourcingSalary: [
      ['月份', '外包公司', '姓名', '身份证号', '人资项目', '财务标准项目', '总金额', '进项税', '入账成本', '薪酬类别', '备注'],
      [1, '外包公司1', '张三', '110101199001011234', '人资项目1', '财务标准项目1', 15000, 595, 15000, '工资', '备注1']
    ],
    socialSecurityAndHousingFund: [
      ['月份', '代缴单位', '个人公积金', '个人养老', '个人医疗', '个人失业', '个人年金', '补充医疗', '单位公积金', '单位养老', '单位医疗', '单位失业', '单位工伤', '单位生育', '企业年金', '合计金额', '备注'],
      [1, '代缴单位1', 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, 15000, '备注1']
    ],
    socialSecurityAndHousingFundAdjustment: [
      [
        '月份', '社保缴纳单位', '公积金缴纳单位', '年金缴纳单位', '身份证号', '姓名', '成本所属项目', '财务标准项目', '财务标准单位', '薪酬类别',
        '本期收入', '个人养老回摊', '个人公积金回摊', '个人医疗回摊',
        '个人失业回摊', '个人年金回摊', '其它扣款', '包干费补贴', '本次个税',
        '单位养老分摊', '单位公积金分摊', '单位医疗分摊', '单位失业分摊',
        '单位工伤分摊', '单位生育分摊', '补充医疗分摊', '其他分摊',
        '总成本', '备注'
      ],
      [
        1, '华南', '武汉1', '美国', '110101199001011234', '张三', '研发项目A', '财务一体化项目1', '财务一体化单位1', '工资',
        15000, 800, 960, 240, 80, 0, 0, 0, 0,
        0, 2080, 4000, 5000, 0, 15000, 11080, 1, 1, '备注1'
      ]
    ]
  }
}

// 处理身份证号和证件号码列为文本格式 - 使用Univer字典对象格式
const processIdColumnsAsText = (data: any[][]): any[][] => {
  if (!data || data.length === 0) return data

  // 获取表头行
  const headerRow = data[0]
  if (!headerRow) return data

  // 找出身份证号和证件号码列的索引
  const idColumns: number[] = []
  headerRow.forEach((header, index) => {
    if (header && typeof header === 'string') {
      const headerStr = header.trim()
      if (headerStr.includes('身份证号') || headerStr.includes('证件号码') || headerStr.includes('银行卡号')) {
        idColumns.push(index)
      }
    }
  })

  // 如果没有找到ID列，直接返回原数据
  if (idColumns.length === 0) return data

  // 处理数据，确保ID列为文本格式 - 使用Univer字典对象格式
  return data.map((row, rowIndex) => {
    if (rowIndex === 0) return row // 保持表头不变

    return row.map((cell, colIndex) => {
      if (idColumns.includes(colIndex) && cell) {
        const cellValue = String(cell).trim()
        // 对于身份证号、证件号码、银行卡号等，使用Univer字典对象格式强制为文本
        if (/^\d+$/.test(cellValue)) {
          return {
            v: cellValue,
            t: 1, // CellValueType.STRING
            s: null
          }
        }
        // 如果已经是字符串但包含数字，也转换为字典格式
        return {
          v: cellValue,
          t: 1, // CellValueType.STRING  
          s: null
        }
      }
      return cell
    })
  })
}

// 数据存储
const tabData = ref(initializeDefaultData())

// 初始化Univer
const initUniver = async () => {
  if (!univerContainer.value) return

  try {
    const { univer, univerAPI } = createUniver({
      locale: LocaleType.ZH_CN,
      locales: {
        [LocaleType.ZH_CN]: merge(
          {},
          UniverPresetSheetsCoreZhCN,
          sheetsConditionalFormattingZhCN,
          sheetsDataValidationZhCN,
          sheetsFilterZhCN
        )
      },
      theme: defaultTheme,
      presets: [
        UniverSheetsCorePreset({
          container: univerContainer.value as HTMLElement
        }),
        UniverSheetsConditionalFormattingPreset(),
        UniverSheetsDataValidationPreset(),
        UniverSheetsFilterPreset()
      ]
    })

    univerInstance = univer
    univerAPIInstance = univerAPI

    // 创建工作簿
    const workbook = univerAPIInstance.createWorkbook({
      id: 'salary-tax-workbook',
      name: '薪酬个税管理'
    })

    // 为每个标签页创建工作表并加载初始数据
    tabs.value.forEach((tab, index) => {
      const sheet = workbook.create(tab.name, 100, 50) // 增加默认列数

      // 加载初始数据
      const initialData = tabData.value[tab.key]
      if (initialData && initialData.length > 0) {
        // 自动调整工作表大小以适应数据
        const requiredRows = Math.max(initialData.length + 10, 100)
        const requiredCols = Math.max(initialData[0].length + 5, 50)

        // 更新工作表大小
        sheet.setRowCount(requiredRows)
        sheet.setColumnCount(requiredCols)

        // 处理身份证号和证件号码列的文本格式
        const processedData = processIdColumnsAsText(initialData)

        // 设置数据
        sheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

        // 设置表头样式
        if (initialData.length > 0) {
          const headerRange = sheet.getRange(0, 0, 1, initialData[0].length)
          headerRange.setFontWeight('bold')
          headerRange.setBackgroundColor('#f0f0f0')
        }
      }

      if (index === 0) {
        // 设置默认激活第一个工作表
        workbook.setActiveSheet(sheet.getSheetId())
      }
    })

    // 删除默认的Sheet1（如果存在）
    try {
      workbook.deleteSheet(workbook.getSheetByName("Sheet1"))
    } catch (error) {
      console.warn('删除Sheet1失败:', error)
    }

    console.log('Univer初始化成功，已加载所有初始数据')
  } catch (error) {
    console.error('Univer初始化失败:', error)
  }
}

// 切换标签页（移除，因为Univer自带tab功能）
// const switchTab = (index: number) => {
//   if (!univerAPIInstance) return
//   
//   activeTabIndex.value = index
//   const workbook = univerAPIInstance.getActiveWorkbook()
//   if (workbook) {
//     const sheets = workbook.getSheets()
//     if (sheets[index]) {
//       workbook.setActiveSheet(sheets[index].getSheetId())
//     }
//   }
// }

// 加载数据到当前工作表（支持自动扩展行列）
const loadDataToCurrentSheet = (data: any[][]) => {
  if (!univerAPIInstance || !data.length) return

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const activeSheet = workbook?.getActiveSheet()

    if (activeSheet && data.length > 0) {
      // 清除现有数据
      activeSheet.clear()

      // 自动调整工作表大小以适应数据
      const requiredRows = Math.max(data.length + 10, 100)
      const requiredCols = Math.max(data[0].length + 5, 50)

      // 更新工作表大小
      activeSheet.setRowCount(requiredRows)
      activeSheet.setColumnCount(requiredCols)

      // 处理身份证号和证件号码列的文本格式
      const processedData = processIdColumnsAsText(data)

      // 设置数据
      activeSheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

      // 设置表头样式
      if (data.length > 0) {
        const headerRange = activeSheet.getRange(0, 0, 1, data[0].length)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      }

      // 更新时间戳
      lastUpdateTime.value = new Date().toLocaleTimeString()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 加载数据到指定工作表（支持自动扩展行列）
const loadDataToSheet = (sheetIndex: number, data: any[][]) => {
  if (!univerAPIInstance) return

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const sheets = workbook?.getSheets()

    if (sheets && sheets[sheetIndex]) {
      const sheet = sheets[sheetIndex]

      // 如果有数据，则设置数据
      if (data.length > 0) {
        // 处理身份证号和证件号码列的文本格式
        let processedData = processIdColumnsAsText(data)

        // 校验每一行长度与表头一致
        const colCount = processedData[0]?.length || 0
        processedData = processedData.map(row => {
          const newRow = Array.from(row)
          if (newRow.length < colCount) {
            // 补齐
            return newRow.concat(Array(colCount - newRow.length).fill(''))
          } else if (newRow.length > colCount) {
            // 截断
            return newRow.slice(0, colCount)
          }
          return newRow
        })

        // 自动调整工作表大小以适应数据
        const requiredRows = Math.max(processedData.length + 10, 100)
        const requiredCols = Math.max(colCount + 5, 50)
        sheet.setRowCount(requiredRows)
        sheet.setColumnCount(requiredCols)

        // 设置数据
        sheet.getRange(0, 0, processedData.length, colCount).setValues(processedData)

        // 设置表头样式
        const headerRange = sheet.getRange(0, 0, 1, colCount)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      } else {
        // 没有数据时，设置默认大小
        sheet.setRowCount(100)
        sheet.setColumnCount(50)
      }

      // 响应式赋值，确保UI同步
      const tabKey = tabs.value[sheetIndex]?.key
      if (tabKey) {
        tabData.value = { ...tabData.value, [tabKey]: data }
      }
    }
  } catch (error) {
    console.error('加载数据到工作表失败:', error)
  }
}

// 从Excel文件导入专项附加扣除数据
const importSpecialDeductionFromExcel = async (file: File, importMode: 'overwrite' | 'append') => {
  try {
    isLoading.value = true

    const workbook = new ExcelJS.Workbook()
    const arrayBuffer = await file.arrayBuffer()
    await workbook.xlsx.load(arrayBuffer)

    // 获取第一个工作表的数据
    const worksheet = workbook.getWorksheet(1)
    if (!worksheet) {
      alert('Excel文件中没有找到工作表！')
      return
    }

    const data: any[][] = []
    const headerRow: any[] = []
    let idColumns: number[] = []

    worksheet.eachRow((row, rowNumber) => {
      const rowData: any[] = []

      // 处理表头行，识别身份证号和证件号码列
      if (rowNumber === 1) {
        row.eachCell((cell, colNumber) => {
          const cellValue = cell.value ? String(cell.value).trim() : ''
          headerRow[colNumber - 1] = cellValue

          // 检查是否为身份证号或证件号码列
          if (cellValue.includes('身份证号') || cellValue.includes('证件号码')) {
            idColumns.push(colNumber - 1)
          }
        })
      }

      // 处理数据行
      row.eachCell((cell, colNumber) => {
        let cellValue = cell.value

        // 如果是身份证号或证件号码列，强制为文本格式
        if (idColumns.includes(colNumber - 1) && cellValue) {
          cellValue = String(cellValue).trim()
          // 确保是文本格式，防止Excel自动转换为数字
          if (/^\d+$/.test(cellValue)) {
            cellValue = "'" + cellValue
          }
        }

        rowData[colNumber - 1] = cellValue
      })

      data.push(rowData)
    })

    if (data.length > 0) {
      // 找到专项附加扣除表工作表
      const workbook = univerAPIInstance?.getActiveWorkbook()
      const sheets = workbook?.getSheets()
      const specialSheet = sheets?.find(sheet => sheet.getSheet().getName() === '累计专项附加扣除表')

      if (!specialSheet) {
        alert('未找到"累计专项附加扣除表"工作表！')
        return
      }

      let finalData: any[][]

      if (importMode === 'overwrite') {
        // 覆盖导入：直接使用新数据
        finalData = data
      } else {
        // 追加导入：获取现有数据并追加
        const maxRow = specialSheet.getMaxRows()
        const maxCol = specialSheet.getMaxColumns()
        const existingData = maxRow > 0 && maxCol > 0 ? specialSheet.getRange(0, 0, maxRow, maxCol).getValues() : []

        if (existingData.length > 0) {
          // 保留表头，追加数据行
          finalData = [...existingData]
          finalData.push(...data.slice(1)) // 跳过导入数据的表头
        } else {
          finalData = data
        }
      }

      // 处理身份证号列为文本格式
      const processedData = processIdColumnsAsText(finalData)

      // 更新工作表
      const requiredRows = Math.max(processedData.length + 10, 100)
      const requiredCols = Math.max(processedData[0]?.length + 5 || 0, 50)
      specialSheet.setRowCount(requiredRows)
      specialSheet.setColumnCount(requiredCols)

      // 清除现有数据并设置新数据
      specialSheet.clear()
      if (processedData.length > 0) {
        specialSheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

        // 设置表头样式
        const headerRange = specialSheet.getRange(0, 0, 1, processedData[0].length)
        headerRange.setFontWeight('bold')
        headerRange.setBackgroundColor('#f0f0f0')
      }

      // 更新本地数据
      tabData.value.specialDeduction = processedData

      const modeText = importMode === 'overwrite' ? '覆盖' : '追加'
      alert(`成功${modeText}导入专项附加扣除数据 ${processedData.length - 1} 行！`)
    } else {
      alert('Excel文件中没有数据！')
    }
  } catch (error) {
    console.error('导入专项附加扣除失败:', error)
    alert('导入专项附加扣除失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 计算社保公积金回摊
const calculateSocialSecurityAllocation = async () => {
  try {
    isLoading.value = true

    // 获取所有需要的工作表数据
    const workbook = univerAPIInstance?.getActiveWorkbook()
    const sheets = workbook?.getSheets()

    if (!sheets) {
      alert('未找到工作簿！')
      return
    }

    // 获取算税底稿数据
    const taxSheet = sheets.find(sheet => sheet.getSheet().getName() === '算税底稿')
    if (!taxSheet) {
      alert('未找到"算税底稿"工作表！')
      return
    }
    const taxMaxRow = taxSheet.getMaxRows()
    const taxMaxCol = taxSheet.getMaxColumns()
    const taxData = taxMaxRow > 0 && taxMaxCol > 0 ? taxSheet.getRange(0, 0, taxMaxRow, taxMaxCol).getValues() : []

    // 获取社保公积金实际缴纳表数据
    const socialSheet = sheets.find(sheet => sheet.getSheet().getName() === '社保公积金实际缴纳表')
    if (!socialSheet) {
      alert('未找到"社保公积金实际缴纳表"工作表！')
      return
    }
    const socialMaxRow = socialSheet.getMaxRows()
    const socialMaxCol = socialSheet.getMaxColumns()
    const socialData = socialMaxRow > 0 && socialMaxCol > 0 ? socialSheet.getRange(0, 0, socialMaxRow, socialMaxCol).getValues() : []

    if (!taxData || taxData.length === 0) {
      alert('算税底稿数据为空！')
      return
    }

    if (!socialData || socialData.length === 0) {
      alert('社保公积金实际缴纳表数据为空！')
      return
    }

    // 发送数据到后台计算回摊
    const response = await fetch('http://localhost:8000/api/calculate-social-security-allocation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taxCalculationData: taxData,
        socialSecurityData: socialData,
        year: selectedYear.value
      })
    })

    if (!response.ok) {
      throw new Error(`计算回摊失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (result.code === 200 && result.data) {
      // 更新薪酬总表(无一次性年终及包干)工作表
      const adjustmentSheet = sheets.find(sheet => sheet.getSheet().getName() === '薪酬总表(无一次性年终及包干)')
      if (adjustmentSheet) {
        const allocationData = result.data

        // 处理身份证号列为文本格式
        const processedData = processIdColumnsAsText(allocationData)

        // 更新工作表
        const requiredRows = Math.max(processedData.length + 10, 100)
        const requiredCols = Math.max(processedData[0]?.length + 5 || 0, 50)
        adjustmentSheet.setRowCount(requiredRows)
        adjustmentSheet.setColumnCount(requiredCols)

        // 清除现有数据并设置新数据
        adjustmentSheet.clear()
        if (processedData.length > 0) {
          adjustmentSheet.getRange(0, 0, processedData.length, processedData[0].length).setValues(processedData)

          // 设置表头样式
          const headerRange = adjustmentSheet.getRange(0, 0, 1, processedData[0].length)
          headerRange.setFontWeight('bold')
          headerRange.setBackgroundColor('#f0f0f0')
        }

        // 更新本地数据
        tabData.value.socialSecurityAndHousingFundAdjustment = processedData

        alert(`社保公积金回摊计算完成！共处理 ${processedData.length - 1} 条记录`)
      } else {
        alert('未找到"薪酬总表(无一次性年终及包干)"工作表！')
      }
    } else {
      alert('计算回摊失败: ' + (result.message || '未知错误'))
    }
  } catch (error) {
    console.error('计算社保公积金回摊失败:', error)
    alert('计算社保公积金回摊失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 获取当前工作表数据
const getCurrentSheetData = (): any[][] => {
  if (!univerAPIInstance) return []

  try {
    const workbook = univerAPIInstance.getActiveWorkbook()
    const activeSheet = workbook?.getActiveSheet()

    if (activeSheet) {
      // 获取工作表的所有数据
      const maxRow = activeSheet.getMaxRows()
      const maxCol = activeSheet.getMaxColumns()

      if (maxRow > 0 && maxCol > 0) {
        const range = activeSheet.getRange(0, 0, maxRow, maxCol)
        return range.getValues()
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }

  return []
}

// 从算税底稿数据中提取月份和薪酬类别
const extractMonthsAndCategories = (data: any[][]) => {
  if (!data || data.length < 2) {
    return { months: [], salaryCategories: [], monthCategoryMap: {} }
  }

  const header = data[0]
  const monthIdx = header.indexOf('月份')
  const categoryIdx = header.indexOf('薪酬类别')

  if (monthIdx === -1 || categoryIdx === -1) {
    return { months: [], salaryCategories: [], monthCategoryMap: {} }
  }

  const monthsSet = new Set<number>()
  const categoriesSet = new Set<string>()
  const monthCategoryMap: Record<string, Set<string>> = {}

  // 从数据行中提取唯一的月份和薪酬类别，建立月份与薪酬类别的映射关系
  for (let i = 1; i < data.length; i++) {
    const row = data[i]
    if (row && row.length > Math.max(monthIdx, categoryIdx)) {
      const monthValue = row[monthIdx]
      const category = row[categoryIdx]

      // 确保月份是正整数
      if (monthValue !== null && monthValue !== undefined && monthValue !== '') {
        const month = Number(monthValue)
        if (Number.isInteger(month) && month > 0) {
          monthsSet.add(month)
          const monthStr = String(month)

          if (category !== null && category !== undefined && category !== '') {
            const categoryStr = String(category).trim()
            categoriesSet.add(categoryStr)

            // 建立月份与薪酬类别的映射
            if (!monthCategoryMap[monthStr]) {
              monthCategoryMap[monthStr] = new Set<string>()
            }
            monthCategoryMap[monthStr].add(categoryStr)
          }
        }
      }
    }
  }

  // 转换Set为Array并排序
  const monthCategoryMapFinal: Record<string, string[]> = {}
  Object.keys(monthCategoryMap).forEach(month => {
    monthCategoryMapFinal[month] = Array.from(monthCategoryMap[month]).sort()
  })

  return {
    months: Array.from(monthsSet).sort((a, b) => a - b).map(String),
    salaryCategories: Array.from(categoriesSet).sort(),
    monthCategoryMap: monthCategoryMapFinal
  }
}

// 显示选择对话框
const showDeclarationDialog = (months: string[], salaryCategories: string[], monthCategoryMap: Record<string, string[]>): Promise<{ months: string[], categories: string[] } | null> => {
  return new Promise((resolve) => {
    // 创建对话框HTML
    const dialogHtml = `
      <div id="declaration-dialog" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
      ">
        <div style="
          background: white;
          padding: 30px;
          border-radius: 8px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
        ">
          <h3 style="margin-top: 0; color: #333; text-align: center;">推送申报设置</h3>
          
          <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #555;">选择月份：</label>
            <select id="month-select" style="
              width: 100%;
              padding: 8px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
            ">
              <option value="">请选择月份</option>
              ${months.map(month => `<option value="${month}">${month}月</option>`).join('')}
            </select>
          </div>
          
          <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #555;">选择薪酬类别（可多选）：</label>
            <div id="category-notice" style="
              color: #666;
              font-size: 12px;
              margin-bottom: 8px;
              font-style: italic;
            ">
              请先选择月份以显示对应的薪酬类别
            </div>
            <div id="category-checkboxes" style="
              max-height: 200px;
              overflow-y: auto;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 10px;
              background: #f9f9f9;
              min-height: 100px;
            ">
              <!-- 薪酬类别选项将根据月份动态生成 -->
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 25px;">
            <button id="confirm-btn" style="
              background: #4CAF50;
              color: white;
              border: none;
              padding: 10px 20px;
              margin-right: 10px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
              transition: background-color 0.3s;
            " onmouseover="this.style.backgroundColor='#45a049'" onmouseout="this.style.backgroundColor='#4CAF50'">
              确认推送
            </button>
            <button id="cancel-btn" style="
              background: #f44336;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 4px;
              cursor: pointer;
              font-size: 14px;
              transition: background-color 0.3s;
            " onmouseover="this.style.backgroundColor='#da190b'" onmouseout="this.style.backgroundColor='#f44336'">
              取消
            </button>
          </div>
        </div>
      </div>
    `

    // 添加对话框到页面
    document.body.insertAdjacentHTML('beforeend', dialogHtml)

    const dialog = document.getElementById('declaration-dialog')
    const monthSelect = document.getElementById('month-select') as HTMLSelectElement
    const categoryCheckboxes = document.getElementById('category-checkboxes')
    const categoryNotice = document.getElementById('category-notice')
    const confirmBtn = document.getElementById('confirm-btn')
    const cancelBtn = document.getElementById('cancel-btn')

    // 更新薪酬类别选项的函数
    const updateCategoryOptions = (selectedMonth: string) => {
      if (!categoryCheckboxes || !categoryNotice) return

      if (!selectedMonth) {
        categoryCheckboxes.innerHTML = ''
        categoryNotice.textContent = '请先选择月份以显示对应的薪酬类别'
        categoryNotice.style.display = 'block'
        return
      }

      const categoriesForMonth = monthCategoryMap[selectedMonth] || []

      if (categoriesForMonth.length === 0) {
        categoryCheckboxes.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">该月份没有薪酬类别数据</div>'
        categoryNotice.style.display = 'none'
        return
      }

      categoryNotice.style.display = 'none'
      categoryCheckboxes.innerHTML = categoriesForMonth.map(category => `
        <label style="
          display: block;
          margin-bottom: 8px;
          cursor: pointer;
          padding: 5px;
          border-radius: 3px;
          transition: background-color 0.2s;
        " onmouseover="this.style.backgroundColor='#e3f2fd'" onmouseout="this.style.backgroundColor='transparent'">
          <input type="checkbox" value="${category}" style="margin-right: 8px;">
          ${category}
        </label>
      `).join('')
    }

    // 月份选择变化事件
    monthSelect?.addEventListener('change', (e) => {
      const selectedMonth = (e.target as HTMLSelectElement).value
      updateCategoryOptions(selectedMonth)
    })

    // 确认按钮事件
    confirmBtn?.addEventListener('click', () => {
      const selectedMonth = monthSelect.value
      const checkboxes = document.querySelectorAll('#category-checkboxes input[type="checkbox"]:checked')
      const selectedCategories = Array.from(checkboxes).map(cb => (cb as HTMLInputElement).value)

      if (!selectedMonth) {
        alert('请选择月份！')
        return
      }

      if (selectedCategories.length === 0) {
        alert('请至少选择一个薪酬类别！')
        return
      }

      dialog?.remove()
      resolve({
        months: [selectedMonth],
        categories: selectedCategories
      })
    })

    // 取消按钮事件
    cancelBtn?.addEventListener('click', () => {
      dialog?.remove()
      resolve(null)
    })

    // 点击背景关闭
    dialog?.addEventListener('click', (e) => {
      if (e.target === dialog) {
        dialog.remove()
        resolve(null)
      }
    })
  })
}

// 核心功能方法（从原始文件迁移）
const matchIdAndProject = async () => {
  try {
    isLoading.value = true;

    // 获取各个表格的数据
    const taxCalculationData = getSheetDataByName('算税底稿');
    const idCardData = getSheetDataByName('身份证号表');
    const projectMappingData = getSheetDataByName('项目匹配表');
    const remoteTaxData = getSheetDataByName('异地纳税');

    if (!taxCalculationData || taxCalculationData.length === 0) {
      alert('请先加载算税底稿数据！');
      return;
    }

    // 获取表头索引
    const taxHeader = taxCalculationData[0];
    const idCardHeader = idCardData?.[0] || [];
    const projectHeader = projectMappingData?.[0] || [];
    const remoteTaxHeader = remoteTaxData?.[0] || [];

    // 算税底稿表头索引
    const taxIdIdx = taxHeader.indexOf('身份证号');
    const taxNameIdx = taxHeader.indexOf('姓名');
    const taxProjectIdx = taxHeader.indexOf('成本所属项目');
    const taxFinanceProjectIdx = taxHeader.indexOf('财务标准项目');
    const taxFinanceUnitIdx = taxHeader.indexOf('财务标准单位');
    const taxMonthIdx = taxHeader.indexOf('月份');
    const taxRegionIdx = taxHeader.indexOf('算税地区');

    // 身份证号表头索引
    const idCardNameIdx = idCardHeader.indexOf('姓名');
    const idCardIdIdx = idCardHeader.indexOf('身份证号');

    // 项目匹配表头索引
    const projectHrIdx = projectHeader.indexOf('人资项目名称');
    const projectStdIdx = projectHeader.indexOf('标准项目名称');
    const projectCodeIdx = projectHeader.indexOf('标准项目编码');
    const projectUnitIdx = projectHeader.indexOf('财务标准单位');
    const projectUnitCodeIdx = projectHeader.indexOf('财务标准单位编码');

    // 异地纳税表头索引
    const remoteTaxIdIdx = remoteTaxHeader.indexOf('身份证号');
    const remoteTaxNameIdx = remoteTaxHeader.indexOf('姓名');
    const monthColumns = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

    // 检查必要字段是否存在
    if (taxIdIdx === -1 || taxNameIdx === -1 || taxProjectIdx === -1 || taxMonthIdx === -1 || taxRegionIdx === -1) {
      alert('算税底稿缺少必要字段：身份证号、姓名、成本所属项目、月份、算税地区');
      return;
    }
    //对身份号表的名字建立索引字典
    let idCardDict = {};
    for (let i = 0; i < idCardData.length; i++) {
      idCardDict[idCardData[i][idCardNameIdx]] = idCardData[i][idCardIdIdx];
    }
    //对异地纳税身份证号建立索引字典
    let remoteDict = {};
    for (let i = 0; i < remoteTaxData.length; i++) {
      remoteDict[remoteTaxData[i][remoteTaxIdIdx]] = i;
    }
    //对项目名称建立字典
    let projectDict = {};
    for (let i = 0; i < projectMappingData.length; i++) {
      projectDict[projectMappingData[i][projectHrIdx]] = i;
    }
    let updateCount = 0;
    let idCardArr = [];
    let remoteArr = [];
    let projectArr = [];
    for (let i = 1; i < taxCalculationData.length; i++) {
      //先匹配身份证号
      taxCalculationData[i][taxIdIdx] = idCardDict[taxCalculationData[i][taxNameIdx]]
      idCardArr.push([taxCalculationData[i][taxIdIdx]]);
      //匹配项目名称，第一个为财务标准项目，第二个财务标准单位，以人资项目名称为索引匹配
      //建立一个一维数组，然后peojectArr添加这个数组
      if (taxCalculationData[i][taxProjectIdx] in projectDict) {
        let ia = projectDict[taxCalculationData[i][taxProjectIdx]];
        projectArr.push([projectMappingData[ia][projectStdIdx], projectMappingData[ia][projectUnitIdx]]);
      } else { projectArr.push(["未找到项目", '']) }

      //匹配异地纳税
      let id = taxCalculationData[i][taxIdIdx]
      let ic = remoteDict[id];
      let ir = taxCalculationData[i][taxMonthIdx] + 2

      if (id in remoteDict) {
        let remotemap = remoteTaxData[ic][ir];
        if (remotemap !== '' && remotemap !== null) {
          remoteArr.push([remotemap])
        } else { remoteArr.push(["注册所在地"]) }
      } else { remoteArr.push(["注册所在地"]) }
      updateCount = updateCount + 1;
    }
    //回写数据
    const workbook = univerAPIInstance?.getActiveWorkbook()
    const sheets = workbook?.getSheets()
    const sheet = sheets.find(sheet => sheet.getSheet().getName() === '算税底稿')
    sheet.getRange(1, taxIdIdx, idCardArr.length, 1).setValues(idCardArr)
    sheet.getRange(1, taxRegionIdx, remoteArr.length, 1).setValues(remoteArr)
    sheet.getRange(1, taxFinanceProjectIdx, projectArr.length, 2).setValues(projectArr)


    alert(`匹配完成！`);
  } catch (error) {
    console.error('匹配失败:', error);
    alert('匹配失败: ' + error.message);
  } finally {
    isLoading.value = false;
  }
}

const calculateTax = async () => {
  try {
    isLoading.value = true

    // ====== 新增：从univer的worksheet获取数据 ======
    if (!univerAPIInstance) {
      alert('Univer尚未初始化！')
      return
    }
    const workbook = univerAPIInstance.getActiveWorkbook()
    if (!workbook) {
      alert('未找到工作簿！')
      return
    }
    // 获取所有sheet
    const sheets = workbook.getSheets()
    // 找到"算税底稿"sheet
    const taxSheet = sheets.find(sheet => sheet.getSheet().getName() === '算税底稿')
    if (!taxSheet) {
      alert('未找到"算税底稿"工作表！')
      return
    }
    // 获取"算税底稿"数据
    const maxRow = taxSheet.getMaxRows()
    const maxCol = taxSheet.getMaxColumns()
    const currentData = maxRow > 0 && maxCol > 0 ? taxSheet.getRange(0, 0, maxRow, maxCol).getValues() : []

    // 找到"累计专项附加扣除表"sheet
    const specialSheet = sheets.find(sheet => sheet.getSheet().getName() === '累计专项附加扣除表')
    let specialDeductionData: any[][] = []
    if (specialSheet) {
      const sMaxRow = specialSheet.getMaxRows()
      const sMaxCol = specialSheet.getMaxColumns()
      specialDeductionData = sMaxRow > 0 && sMaxCol > 0 ? specialSheet.getRange(0, 0, sMaxRow, sMaxCol).getValues() : []
    }
    // ====== 新增结束 ======

    if (!currentData || currentData.length === 0) {
      alert('请先加载算税底稿数据！')
      return
    }

    const result = await taxCalculatorTs(currentData, specialDeductionData)

    // 更新数据到算税底稿工作表
    taxSheet.getRange(0, 0, result.length, result[0].length).setValues(result as any)

    alert('个税计算完成！')
  } catch (error) {
    console.error('计算个税失败:', error)
    alert('计算个税失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const generateDeclaration = async () => {
  try {
    isLoading.value = true

    // 获取算税底稿数据
    const taxCalculationData = tabData.value.taxCalculation
    if (!taxCalculationData || taxCalculationData.length === 0) {
      alert('请先计算个税！')
      return
    }

    // 1. 获取所有月份
    const header = taxCalculationData[0]
    const monthIdx = header.indexOf('月份')
    if (monthIdx === -1) {
      alert('算税底稿缺少"月份"字段！')
      return
    }

    const months = Array.from(new Set(taxCalculationData.slice(1).map(row => String(row[monthIdx]))))

    // 2. 选择月份
    const selectedMonth = await selectMonth(months)
    if (!selectedMonth) return

    // 3. 获取专项附加扣除数据
    const specialDeductionData = tabData.value.specialDeduction

    // 4. 生成申报表数据
    const declarationData = await generateDeclarationData(
      taxCalculationData,
      specialDeductionData,
      selectedMonth
    )

    // 5. 加载数据到申报表工作表
    tabData.value.taxDeclaration = declarationData
    loadDataToSheet(0, declarationData)

    alert(`成功生成${selectedMonth}月份申报表，共${declarationData.length - 1}条记录！`)
  } catch (error) {
    console.error('生成申报表失败:', error)
    alert('生成申报表失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 选择月份的弹窗
const selectMonth = (months: string[]): Promise<string | null> => {
  return new Promise((resolve) => {
    const monthOptions = months.map(m => `<option value="${m}">${m}</option>`).join('')
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;`
    dialog.innerHTML = `
      <div style="
        background: white; padding: 32px; border-radius: 12px; min-width: 320px; max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: fadeInScale 0.2s ease-out;
      ">
        <style>
          @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
          }
        </style>
        <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 18px; font-weight: 600;">
          选择申报月份
        </h3>
        <select id="monthSelect" style="
          width: 100%; padding: 12px; margin: 0 0 24px 0; border: 2px solid #e5e7eb;
          border-radius: 8px; font-size: 14px; background: white; outline: none;
          transition: border-color 0.2s;
        " onmouseover="this.style.borderColor='#3b82f6'" onmouseout="this.style.borderColor='#e5e7eb'">
          ${monthOptions}
        </select>
        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="cancelBtn" style="
            padding: 10px 20px; border: 2px solid #e5e7eb; background: white; color: #6b7280;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s;
          " onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#d1d5db';"
             onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb';">
            取消
          </button>
          <button id="okBtn" style="
            padding: 10px 20px; border: none; background: #3b82f6; color: white;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: background-color 0.2s;
          " onmouseover="this.style.backgroundColor='#2563eb';"
             onmouseout="this.style.backgroundColor='#3b82f6';">
            确定
          </button>
        </div>
      </div>`
    document.body.appendChild(dialog)

    const cleanup = () => {
      if (document.body.contains(dialog)) {
        document.body.removeChild(dialog)
      }
    }

      ; (dialog.querySelector('#cancelBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve(null) }
      ; (dialog.querySelector('#okBtn') as HTMLButtonElement).onclick = () => {
        const val = (dialog.querySelector('#monthSelect') as HTMLSelectElement).value
        cleanup()
        resolve(val)
      }

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup()
        resolve(null)
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)
  })
}

// 生成申报表数据
const generateDeclarationData = async (
  taxCalculationData: any[][],
  specialDeductionData: any[][],
  selectedMonth: string
): Promise<any[][]> => {
  const header = taxCalculationData[0]
  const monthIdx = header.indexOf('月份')
  const idIdx = header.indexOf('身份证号')
  const regionIdx = header.indexOf('算税地区')
  const nameIdx = header.indexOf('姓名')
  const empIdIdx = header.indexOf('工号')

  if (idIdx === -1 || regionIdx === -1) {
    throw new Error('算税底稿缺少"身份证号"或"算税地区"字段！')
  }

  // 申报表字段定义
  const declarationHeader = [
    '工号', '姓名', '证件类型', '证件号码', '本期收入', '本期免税收入',
    '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金',
    '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
    '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金',
    '企业(职业)年金', '商业健康保险', '税延养老保险', '其他',
    '准予扣除的捐赠额', '减免税额', '备注'
  ]

  // 筛选指定月份的数据
  const filteredRows = taxCalculationData.slice(1).filter(row => row[monthIdx] === selectedMonth)

  // 按身份证号+算税地区分组合并
  const groupedData = new Map()

  filteredRows.forEach(row => {
    const id = row[idIdx]
    const region = row[regionIdx]
    const key = `${id}_${region}`

    if (!groupedData.has(key)) {
      groupedData.set(key, {
        工号: row[empIdIdx] || '',
        姓名: row[nameIdx] || '',
        证件类型: '居民身份证',
        证件号码: id,
        本期收入: 0,
        本期免税收入: 0,
        基本养老保险费: 0,
        基本医疗保险费: 0,
        失业保险费: 0,
        住房公积金: 0,
        累计子女教育: 0,
        累计继续教育: 0,
        累计住房贷款利息: 0,
        累计住房租金: 0,
        累计赡养老人: 0,
        累计3岁以下婴幼儿照护: 0,
        累计个人养老金: 0,
        企业职业年金: 0,
        商业健康保险: 0,
        税延养老保险: 0,
        其他: 0,
        准予扣除的捐赠额: 0,
        减免税额: 0,
        备注: ''
      })
    }

    const record = groupedData.get(key)

    // 累加收入和扣除项
    const incomeIdx = header.indexOf('本期收入')
    const taxFreeIncomeIdx = header.indexOf('本期免税收入')
    const pensionIdx = header.indexOf('基本养老保险费')
    const medicalIdx = header.indexOf('基本医疗保险费')
    const unemploymentIdx = header.indexOf('失业保险费')
    const housingFundIdx = header.indexOf('住房公积金')

    if (incomeIdx !== -1) record!.本期收入 += parseFloat(row[incomeIdx]) || 0
    if (taxFreeIncomeIdx !== -1) record!.本期免税收入 += parseFloat(row[taxFreeIncomeIdx]) || 0
    if (pensionIdx !== -1) record!.基本养老保险费 += parseFloat(row[pensionIdx]) || 0
    if (medicalIdx !== -1) record!.基本医疗保险费 += parseFloat(row[medicalIdx]) || 0
    if (unemploymentIdx !== -1) record!.失业保险费 += parseFloat(row[unemploymentIdx]) || 0
    if (housingFundIdx !== -1) record!.住房公积金 += parseFloat(row[housingFundIdx]) || 0
  })

  // 合并专项附加扣除数据
  if (specialDeductionData && specialDeductionData.length > 0) {
    const specialHeader = specialDeductionData[0]
    const specialIdIdx = specialHeader.indexOf('证件号码')

    if (specialIdIdx !== -1) {
      specialDeductionData.slice(1).forEach(row => {
        const id = row[specialIdIdx]

        // 查找匹配的记录
        groupedData.forEach((record, key) => {
          if (key.startsWith(id + '_')) {
            // 累加专项附加扣除
            const specialFields = [
              '累计子女教育', '累计继续教育', '累计住房贷款利息', '累计住房租金',
              '累计赡养老人', '累计3岁以下婴幼儿照护', '累计个人养老金'
            ]

            specialFields.forEach(field => {
              const fieldIdx = specialHeader.indexOf(field)
              if (fieldIdx !== -1) {
                record[field] += parseFloat(row[fieldIdx]) || 0
              }
            })
          }
        })
      })
    }
  }

  // 转换为二维数组
  const result = [declarationHeader]
  groupedData.forEach(record => {
    const row = declarationHeader.map(field => record[field] || 0)
    result.push(row)
  })

  return result
}

const generateDivision = async () => {
  // TODO: 实现生成劳务派遣分割表功能
  console.log('生成劳务派遣分割表')
}

const pushDeclaration = async () => {
  try {
    isLoading.value = true

    // 获取算税底稿数据
    const taxCalculationData = getSheetDataByName('算税底稿');
    const idCardData=getSheetDataByName('身份证号表');
    const projectMappingData=getSheetDataByName('项目匹配表');

    if (!taxCalculationData || taxCalculationData.length === 0) {
      alert('请先加载算税底稿数据！')
      return
    }

    // 提取月份和薪酬类别
    const { months, salaryCategories, monthCategoryMap } = extractMonthsAndCategories(taxCalculationData)

    if (months.length === 0) {
      alert('算税底稿中没有找到月份数据！')
      return
    }

    if (salaryCategories.length === 0) {
      alert('算税底稿中没有找到薪酬类别数据！')
      return
    }

    // 显示选择对话框
    const selectedOptions = await showDeclarationDialog(months, salaryCategories, monthCategoryMap)

    if (!selectedOptions) {
      return // 用户取消了操作
    }

    // 发送数据到后台处理
    const response = await fetch('http://localhost:8000/api/push-declaration', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taxCalculationData: taxCalculationData,
        selectedMonths: selectedOptions.months,
        selectedCategories: selectedOptions.categories,
        idCardData:idCardData,
        projectMappingData:projectMappingData,
        year: selectedYear.value
      })
    })

    if (!response.ok) {
      throw new Error(`推送申报失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      alert(`推送申报成功！${result.message || ''}`)
    } else {
      alert('推送申报失败: ' + (result.message || '未知错误'))
    }

  } catch (error) {
    console.error('推送申报失败:', error)
    alert('推送申报失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const buildFinanceTemplate = async () => {
  try {
    isLoading.value = true

    // 获取薪酬总表(无一次性年终及包干)数据
    const workbook = univerAPIInstance?.getActiveWorkbook()
    const sheets = workbook?.getSheets()

    if (!sheets) {
      alert('未找到工作簿！')
      return
    }

    const adjustmentSheet = sheets.find(sheet => sheet.getSheet().getName() === '薪酬总表(无一次性年终及包干)')
    if (!adjustmentSheet) {
      alert('未找到"薪酬总表(无一次性年终及包干)"工作表！请先计算社保公积金回摊。')
      return
    }

    const maxRow = adjustmentSheet.getMaxRows()
    const maxCol = adjustmentSheet.getMaxColumns()
    const adjustmentData = maxRow > 0 && maxCol > 0 ? adjustmentSheet.getRange(0, 0, maxRow, maxCol).getValues() : []

    if (!adjustmentData || adjustmentData.length === 0) {
      alert('薪酬总表数据为空！请先计算社保公积金回摊。')
      return
    }

    // 发送数据到后台构建财务一体化模板
    const response = await fetch('http://localhost:8000/api/build-finance-template', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        adjustmentData: adjustmentData,
        year: selectedYear.value,
        templateType: 'socialSecurityAndHousingFund'
      })
    })

    if (!response.ok) {
      throw new Error(`构建财务模板失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()

    if (result.code === 200) {
      alert('财务一体化计提发放模板(社保公积金表)构建成功！')
      console.log('模板构建结果:', result)
    } else {
      alert('构建财务模板失败: ' + (result.message || '未知错误'))
    }
  } catch (error) {
    console.error('构建财务模板失败:', error)
    alert('构建财务模板失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const exportAllTablesToExcel = async () => {
  try {
    isLoading.value = true

    const workbook = new ExcelJS.Workbook()

    // 为每个标签页创建工作表并导出数据
    for (let i = 0; i < tabs.value.length; i++) {
      const tab = tabs.value[i]
      const data = tabData.value[tab.key] || []

      if (data.length > 0) {
        const worksheet = workbook.addWorksheet(tab.name)

        // 添加数据
        data.forEach((row: any[], index: number) => {
          worksheet.addRow(row)

          // 设置表头样式
          if (index === 0) {
            const headerRow = worksheet.getRow(1)
            headerRow.font = { bold: true }
            headerRow.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF0F0F0' }
            }
          }
        })

        // 自动调整列宽
        worksheet.columns.forEach(column => {
          column.width = 15
        })
      }
    }

    // 如果没有数据，至少创建一个空工作表
    if (workbook.worksheets.length === 0) {
      workbook.addWorksheet('空数据')
    }

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `薪酬个税数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    alert('导出成功！')
  } catch (error) {
    console.error('导出所有表格失败:', error)
    alert('导出所有表格失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

// 触发专项附加扣除导入
const triggerSpecialDeductionImport = () => {
  if (fileInput.value) {
    fileInput.value.click()
  }
}

// 处理专项附加扣除导入
const handleSpecialDeductionImport = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // 弹出选择导入模式的对话框
    const importMode = await selectImportMode()
    if (importMode) {
      await importSpecialDeductionFromExcel(file, importMode)
    }
    // 清空文件输入，允许重复选择同一文件
    target.value = ''
  }
}

// 选择导入模式的弹窗
const selectImportMode = (): Promise<'overwrite' | 'append' | null> => {
  return new Promise((resolve) => {
    const dialog = document.createElement('div')
    dialog.style.cssText = `
      position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; z-index: 9999;
      background: rgba(0, 0, 0, 0.5); display: flex; align-items: center; justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;`
    dialog.innerHTML = `
      <div style="
        background: white; padding: 32px; border-radius: 12px; min-width: 320px; max-width: 400px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: fadeInScale 0.2s ease-out;
      ">
        <style>
          @keyframes fadeInScale {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
          }
        </style>
        <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 18px; font-weight: 600;">
          选择导入模式
        </h3>
        <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 14px; line-height: 1.5;">
          请选择专项附加扣除数据的导入方式：
        </p>
        <div style="display: flex; flex-direction: column; gap: 12px; margin-bottom: 24px;">
          <button id="overwriteBtn" style="
            padding: 12px 16px; border: 2px solid #3b82f6; background: #3b82f6; color: white;
            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s; text-align: left;
          " onmouseover="this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';"
             onmouseout="this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';">
            <strong>覆盖导入</strong><br>
            <small style="opacity: 0.8;">清空现有数据，使用新数据替换</small>
          </button>
          <button id="appendBtn" style="
            padding: 12px 16px; border: 2px solid #10b981; background: #10b981; color: white;
            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s; text-align: left;
          " onmouseover="this.style.backgroundColor='#059669'; this.style.borderColor='#059669';"
             onmouseout="this.style.backgroundColor='#10b981'; this.style.borderColor='#10b981';">
            <strong>追加导入</strong><br>
            <small style="opacity: 0.8;">保留现有数据，在末尾添加新数据</small>
          </button>
        </div>
        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="cancelBtn" style="
            padding: 10px 20px; border: 2px solid #e5e7eb; background: white; color: #6b7280;
            border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500;
            transition: all 0.2s;
          " onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#d1d5db';"
             onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb';">
            取消
          </button>
        </div>
      </div>`
    document.body.appendChild(dialog)

    const cleanup = () => {
      if (document.body.contains(dialog)) {
        document.body.removeChild(dialog)
      }
    }

      ; (dialog.querySelector('#cancelBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve(null) }
      ; (dialog.querySelector('#overwriteBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve('overwrite') }
      ; (dialog.querySelector('#appendBtn') as HTMLButtonElement).onclick = () => { cleanup(); resolve('append') }

    // ESC键关闭
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        cleanup()
        resolve(null)
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)
  })
}

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initUniver()

  // 不需要再次加载数据，initUniver已经加载了所有初始数据到对应的工作表
  // setTimeout(() => {
  //   loadDataToCurrentSheet(tabData.value.taxDeclaration)
  // }, 1000)
})

// 组件卸载
onBeforeUnmount(() => {
  if (univerInstance) {
    univerInstance.dispose()
  }
})

// 获取当前工作表行数
const getCurrentSheetRowCount = (): number => {
  const data = getCurrentSheetData()
  return Math.max(0, data.length - 1) // 减去表头行
}

// 刷新当前工作表
const refreshCurrentSheet = () => {
  const tabKey = tabs.value[activeTabIndex.value]?.key
  if (tabKey && tabData.value[tabKey]) {
    loadDataToCurrentSheet(tabData.value[tabKey])
    lastUpdateTime.value = new Date().toLocaleTimeString()
  }
}

// 清空当前工作表
const clearCurrentSheet = () => {
  if (confirm('确定要清空当前工作表的所有数据吗？此操作不可撤销。')) {
    try {
      const workbook = univerAPIInstance?.getActiveWorkbook()
      const activeSheet = workbook?.getActiveSheet()

      if (activeSheet) {
        activeSheet.clear()

        // 清空本地数据
        const tabKey = tabs.value[activeTabIndex.value]?.key
        if (tabKey) {
          tabData.value[tabKey] = []
        }

        lastUpdateTime.value = new Date().toLocaleTimeString()
        alert('工作表已清空！')
      }
    } catch (error) {
      console.error('清空工作表失败:', error)
      alert('清空工作表失败: ' + error.message)
    }
  }
}

// 更新最后更新时间
const updateLastUpdateTime = () => {
  lastUpdateTime.value = new Date().toLocaleTimeString()
}

// 按年获取后台数据
const fetchAllData = async () => {
  if (!selectedYear.value) {
    alert('请先选择年份！')
    return
  }
  try {
    const response = await fetch('http://localhost:8000/api/get-salay-snapsheet', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ year: selectedYear.value })
    });

    if (!response.ok) {
      throw new Error(`获取快照失败: ${response.status} ${response.statusText}`);
    }

    const snapsheet = await response.json();
    const unitId = univerAPIInstance?.getActiveWorkbook()?.getId();
    if (unitId) {
      univerAPIInstance?.disposeUnit(unitId);
    }
    univerAPIInstance?.createWorkbook(snapsheet);
  } catch (error) {
    console.error('导入数据时出错:', error);
    alert('导入数据失败: ' + (error.message || '未知错误'));
  }
}

const saveAllData = async () => {
  if (!selectedYear.value) {
    alert('请先选择年份！')
    return
  }
  const snapsheet = univerAPIInstance?.getActiveWorkbook()?.save();
  try {
    console.log('开始保存快照...');
    const response = await fetch('http://localhost:8000/api/save-salay-snapshot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        year: selectedYear.value,
        snapshot: snapsheet
      }),
      cache: 'no-cache'
    });

    console.log('请求发送完成，状态:', response.status, response.ok ? '成功' : '失败');

    if (!response.ok) {
      throw new Error(`保存快照失败: ${response.status} ${response.statusText}`);
    }

    alert('快照已成功保存！');
  } catch (error) {
    console.error('保存快照出错:', error);
    alert('保存快照失败: ' + error.message);
  }
}


// 初始化加载默认数据
const loadInitialData = () => {
  const defaultData = initializeDefaultData()
  tabData.value = defaultData

  // 加载默认数据到当前标签页
  if (activeTabIndex.value === 0) {
    loadDataToCurrentSheet(defaultData.taxDeclaration)
  }
}

function getSheetDataByName(sheetName: string): any[][] {
  if (!univerAPIInstance) return [];
  const workbook = univerAPIInstance.getActiveWorkbook();
  const sheets = workbook?.getSheets();
  const sheet = sheets?.find(s => s.getSheet().getName() === sheetName);
  if (!sheet) return [];
  const maxRow = sheet.getMaxRows();
  const maxCol = sheet.getMaxColumns();
  if (maxRow > 0 && maxCol > 0) {
    return sheet.getRange(0, 0, maxRow, maxCol).getValues();
  }
  return [];
}
</script>

<style scoped>
.salary-tax2-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 紧凑头部样式 */
.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 30px;
  min-height: 60px;
}

.header-content {
  display: flex;
  align-items: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-icon {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 30px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-icon {
  font-size: 16px;
}

/* 移除Tab相关样式，因为Univer自带Tab功能 */

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: white;
  font-size: 16px;
  margin-top: 16px;
  font-weight: 500;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.univer-container {
  flex: 1;
  background: white;
  margin: 0 20px 20px 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  min-height: 600px;
}

.univer-container.loading {
  pointer-events: none;
  opacity: 0.7;
}

.univer-spreadsheet {
  width: 100%;
  height: 100%;
  min-height: 600px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .compact-header {
    flex-direction: column;
    gap: 12px;
    padding: 15px 20px;
  }

  .header-controls {
    width: 100%;
    justify-content: flex-start;
  }

  .page-title {
    font-size: 18px;
  }

  .action-buttons {
    padding: 12px 20px;
  }

  .action-button {
    padding: 8px 10px;
    font-size: 11px;
  }

  .univer-container {
    margin: 0 15px 15px 15px;
  }

  .year-select,
  .fetch-button,
  .save-button {
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* 年份选择和数据控制按钮样式 */
.year-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  backdrop-filter: blur(10px);
}

.year-select:focus {
  border-color: rgba(255, 255, 255, 0.4);
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.year-select option {
  background: #333;
  color: white;
}

.fetch-button,
.save-button {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
}

.fetch-button {
  background: rgba(16, 185, 129, 0.8);
}

.save-button {
  background: rgba(59, 130, 246, 0.8);
}

.fetch-button:hover:not(:disabled),
.save-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.fetch-button:disabled,
.save-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}
</style>
