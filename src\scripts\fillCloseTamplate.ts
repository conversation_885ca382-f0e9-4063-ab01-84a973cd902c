import { FUniver } from '@univerjs/presets';

/**
 * 辅助函数：根据列名查找列索引
 * @param headerRow 表头行数据
 * @param columnNames 要查找的列名数组
 * @returns 列名到索引的映射对象
 */
function findColumnIndexes(headerRow: any[], columnNames: string[]): { [key: string]: number } {
    const indexes: { [key: string]: number } = {};
    const columnSet = new Set(columnNames); // 使用Set提高查找性能

    for (let i = 0; i < headerRow.length; i++) {
        const header = headerRow[i];
        if (header && typeof header === 'string' && columnSet.has(header)) {
            indexes[header] = i;
            columnSet.delete(header); // 找到后删除，避免重复查找

            // 如果所有列都找到了，提前退出
            if (columnSet.size === 0) {
                break;
            }
        }
    }

    return indexes;
}

/**
 * 辅助函数：批量设置单元格值
 * @param sheet 工作表对象
 * @param rowIndex 行索引
 * @param columnData 列数据数组 [{column: string, value: any}]
 * @param columnIndexes 列索引映射
 */
function batchSetCellValues(
    sheet: any,
    rowIndex: number,
    columnData: Array<{column: string, value: any}>,
    columnIndexes: { [key: string]: number }
): void {
    columnData.forEach(({ column, value }) => {
        const colIndex = columnIndexes[column];
        if (colIndex !== undefined && value !== undefined && value !== null) {
            sheet.getRange(rowIndex, colIndex).setValue(value);
        }
    });
}

/**
 * 填充安全费结账模板
 * @param univerAPIInstance Univer API实例
 */
export default async function fillCloseTamplate(univerAPIInstance: FUniver): Promise<void> {
    try {
        // 获取快速取数sheet
        const quickDataSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速取数');
        if (!quickDataSheet) {
            console.error('未找到快速取数sheet');
            return;
        }

        // 获取快速取数数据
        const quickDataRange = quickDataSheet.getRange(0, 0, quickDataSheet.getLastRow() + 1, quickDataSheet.getLastColumn() + 1);
        const quickDataArr = quickDataRange.getValues();

        if (!quickDataArr || quickDataArr.length === 0) {
            console.error('快速取数sheet无数据');
            return;
        }

        // 查找快速取数sheet中的列索引
        const quickDataColumns = findColumnIndexes(quickDataArr[0], ['利润中心组名称', '项目名称', '专项储备余额']);

        // 验证必要列是否存在
        const requiredQuickColumns = ['利润中心组名称', '项目名称', '专项储备余额'];
        for (const column of requiredQuickColumns) {
            if (!(column in quickDataColumns)) {
                console.error(`快速取数sheet中未找到${column}列`);
                return;
            }
        }

        // 获取独立结账模板安全费sheet
        const safetyFeeSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板安全费');
        if (!safetyFeeSheet) {
            console.error('未找到独立结账模板安全费sheet');
            return;
        }

        // 获取安全费模板表头
        const headerRange = safetyFeeSheet.getRange(0, 0, 1, safetyFeeSheet.getLastColumn() + 1);
        const headerArr = headerRange.getValues();

        if (!headerArr || headerArr.length === 0) {
            console.error('独立结账模板安全费sheet无表头数据');
            return;
        }

        // 查找安全费模板sheet中的列索引
        const safetyFeeColumns = findColumnIndexes(headerArr[0], ['组织机构', '项目名称', '专项储备余额', '是否']);

        // 验证必要列是否存在
        const requiredSafetyColumns = ['组织机构', '项目名称', '专项储备余额', '是否'];
        for (const column of requiredSafetyColumns) {
            if (!(column in safetyFeeColumns)) {
                console.error(`独立结账模板安全费sheet中未找到${column}列`);
                return;
            }
        }

        // 清理现有数据（保留表头和第一行数据）
        const currentRowCount = safetyFeeSheet.getLastRow();
        if (currentRowCount > 2) {
            safetyFeeSheet.deleteRows(3, currentRowCount - 2);
        }

        // 设置默认值
        safetyFeeSheet.getRange(1, safetyFeeColumns['是否']).setValue('否');

        // 处理数据填充
        let insertRowIndex = 2;
        for (let i = 1; i < quickDataArr.length; i++) {
            const reserveValue = Number(quickDataArr[i][quickDataColumns['专项储备余额']]) || 0;

            // 只处理专项储备余额不为0的记录（考虑浮点数精度）
            if (Math.abs(reserveValue) > 0.001) {
                // 插入新行
                safetyFeeSheet.insertRowAfter(insertRowIndex);

                // 填充数据（添加空值检查）
                const fillData = [
                    { column: '组织机构', value: quickDataArr[i][quickDataColumns['利润中心组名称']] || '' },
                    { column: '项目名称', value: quickDataArr[i][quickDataColumns['项目名称']] || '' },
                    { column: '专项储备余额', value: reserveValue },
                    { column: '是否', value: '是' }
                ];

                // 使用批量设置函数提高性能
                batchSetCellValues(safetyFeeSheet, insertRowIndex, fillData, safetyFeeColumns);

                insertRowIndex++;
            }
        }

        console.log(`成功填充安全费模板，共处理${insertRowIndex - 2}条记录`);

    } catch (error) {
        console.error('填充安全费模板时发生错误:', error);
        throw error;
    }
}

/**
 * 填充收入成本结账模板
 * @param univerAPIInstance Univer API实例
 */
export async function fillCloseTamplate2(univerAPIInstance: FUniver): Promise<void> {
    try {
        // 获取收入成本测算sheet
        const incomeSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
        if (!incomeSheet) {
            console.error('未找到收入成本测算sheet');
            return;
        }

        // 获取收入成本测算数据
        const incomeRange = incomeSheet.getRange(0, 0, incomeSheet.getLastRow() + 1, incomeSheet.getLastColumn() + 1);
        const incomeArr = incomeRange.getValues();

        if (!incomeArr || incomeArr.length === 0) {
            console.error('收入成本测算sheet无数据');
            return;
        }

        // 查找收入成本测算sheet中的列索引
        const incomeColumns = findColumnIndexes(incomeArr[0], [
            '组织机构', '项目名称', '项目状态', '利润中心编码', '项目编码',
            '结账预计收入', '结账预计成本', '收入合同编号', '本期毛利'
        ]);

        // 验证必要列是否存在
        const requiredIncomeColumns = [
            '组织机构', '项目名称', '项目状态', '利润中心编码', '项目编码',
            '结账预计收入', '结账预计成本', '收入合同编号', '本期毛利'
        ];
        for (const column of requiredIncomeColumns) {
            if (!(column in incomeColumns)) {
                console.error(`收入成本测算sheet中未找到${column}列`);
                return;
            }
        }

        // 获取独立结账模板收入成本sheet
        const templateSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('独立结账模板收入成本');
        if (!templateSheet) {
            console.error('未找到独立结账模板收入成本sheet');
            return;
        }

        // 获取模板表头
        const templateHeaderRange = templateSheet.getRange(0, 0, 1, templateSheet.getLastColumn() + 1);
        const templateHeaderArr = templateHeaderRange.getValues();

        if (!templateHeaderArr || templateHeaderArr.length === 0) {
            console.error('独立结账模板收入成本sheet无表头数据');
            return;
        }

        // 查找模板sheet中的列索引
        const templateColumns = findColumnIndexes(templateHeaderArr[0], [
            '组织机构', '项目名称', '项目状态', '利润中心编码', '项目编码',
            '结账预计收入', '结账预计成本', '收入合同编号'
        ]);

        // 验证必要列是否存在
        const requiredTemplateColumns = [
            '组织机构', '项目名称', '项目状态', '利润中心编码', '项目编码',
            '结账预计收入', '结账预计成本', '收入合同编号'
        ];
        for (const column of requiredTemplateColumns) {
            if (!(column in templateColumns)) {
                console.error(`独立结账模板收入成本sheet中未找到${column}列`);
                return;
            }
        }

        // 清理现有数据（保留表头和第一行数据）
        const currentRowCount = templateSheet.getLastRow();
        if (currentRowCount > 2) {
            templateSheet.deleteRows(3, currentRowCount - 2);
        }

        // 处理数据填充
        let insertRowIndex = 2;
        for (let i = 1; i < incomeArr.length; i++) {
            const profitValue = Number(incomeArr[i][incomeColumns['本期毛利']]) || 0;

            // 只处理本期毛利不为0的记录（考虑浮点数精度）
            if (Math.abs(profitValue) > 0.001) {
                // 插入新行
                templateSheet.insertRowAfter(insertRowIndex);

                // 填充数据（添加空值检查）
                const fillData = [
                    { column: '组织机构', value: incomeArr[i][incomeColumns['组织机构']] || '' },
                    { column: '项目名称', value: incomeArr[i][incomeColumns['项目名称']] || '' },
                    { column: '项目状态', value: incomeArr[i][incomeColumns['项目状态']] || '' },
                    { column: '利润中心编码', value: incomeArr[i][incomeColumns['利润中心编码']] || '' },
                    { column: '项目编码', value: incomeArr[i][incomeColumns['项目编码']] || '' },
                    { column: '结账预计收入', value: incomeArr[i][incomeColumns['结账预计收入']] || 0 },
                    { column: '结账预计成本', value: incomeArr[i][incomeColumns['结账预计成本']] || 0 },
                    { column: '收入合同编号', value: incomeArr[i][incomeColumns['收入合同编号']] || '' }
                ];

                // 使用批量设置函数提高性能
                batchSetCellValues(templateSheet, insertRowIndex, fillData, templateColumns);

                insertRowIndex++;
            }
        }

        console.log(`成功填充收入成本模板，共处理${insertRowIndex - 2}条记录`);

    } catch (error) {
        console.error('填充收入成本模板时发生错误:', error);
        throw error;
    }
}

/**
 * 根据定位符列将快速取数sheet中的数据补全到收入成本测算sheet中
 * 处理列名映射关系，并从上一行补全公式
 * 优化版本：使用getFormulas()批量获取公式，使用setValues()批量回写
 * @param univerAPIInstance Univer API实例
 */
export async function fillRedTextToIncomeSheet(univerAPIInstance: FUniver): Promise<void> {
    try {
        // 获取快速取数sheet
        const quickDataSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('快速取数');
        if (!quickDataSheet) {
            console.error('未找到快速取数sheet');
            return;
        }

        // 获取收入成本测算sheet
        const incomeSheet = univerAPIInstance?.getActiveWorkbook()?.getSheetByName('收入成本测算');
        if (!incomeSheet) {
            console.error('未找到收入成本测算sheet');
            return;
        }

        // 获取快速取数sheet的数据
        const quickDataArr = quickDataSheet.getRange(0, 0, quickDataSheet.getLastRow() + 1, quickDataSheet.getLastColumn() + 1).getValues();

        // 获取收入成本测算sheet的数据
        const incomeArr = incomeSheet.getRange(0, 0, incomeSheet.getLastRow() + 1, incomeSheet.getLastColumn() + 1).getValues();

        // 定义列名映射关系：收入成本测算列名 -> 快速取数列名
        const columnMapping = {
            '定位符': '定位符',
            '组织机构': '利润中心组名称',
            '项目名称': '项目名称',
            '利润中心编码': '利润中心',
            '项目编码': 'WBS元素',
        };

        // 使用优化的列索引查找函数
        const quickColumnIndexes = findColumnIndexes(quickDataArr[0], Object.values(columnMapping));
        const incomeColumnIndexes = findColumnIndexes(incomeArr[0], Object.keys(columnMapping));

        // 检查定位符列是否存在
        if (!quickColumnIndexes.hasOwnProperty('定位符')) {
            console.error('快速取数sheet中未找到定位符列');
            return;
        }
        if (!incomeColumnIndexes.hasOwnProperty('定位符')) {
            console.error('收入成本测算sheet中未找到定位符列');
            return;
        }

        // 建立快速取数sheet的定位符映射
        const quickDataMap = new Map();
        for (let i = 1; i < quickDataArr.length; i++) {
            const locatorValue = quickDataArr[i][quickColumnIndexes['定位符']];
            if (locatorValue) {
                quickDataMap.set(locatorValue, quickDataArr[i]);
            }
        }

        // 建立收入成本测算sheet中现有定位符的集合
        const existingLocators = new Set();
        for (let i = 1; i < incomeArr.length; i++) {
            const locatorValue = incomeArr[i][incomeColumnIndexes['定位符']];
            if (locatorValue) {
                existingLocators.add(locatorValue);
            }
        }

        // 找到需要补全的定位符（在快速取数中存在但在收入成本测算中不存在的）
        const missingLocators: Array<{locator: any, quickRowData: any}> = [];
        quickDataMap.forEach((quickRowData, locator) => {
            if (!existingLocators.has(locator)) {
                missingLocators.push({
                    locator: locator,
                    quickRowData: quickRowData
                });
            }
        });

        if (missingLocators.length === 0) {
            console.log('没有需要补全的数据');
            return;
        }

        console.log(`找到${missingLocators.length}行需要补全的数据`);

        // 获取非标题第一行的公式（优化：批量获取公式）
        let templateFormulas: string[] = [];
        const templateRowIndex = 1; // 非标题第一行
        if (incomeArr.length > templateRowIndex) {
            try {
                // 使用getFormulas()方法批量获取公式
                const formulaRange = incomeSheet.getRange(templateRowIndex, 0, 1, incomeArr[0].length);
                const formulas = formulaRange.getFormulas();
                if (formulas && formulas[0]) {
                    templateFormulas = formulas[0];
                } else {
                    // 如果getFormulas()不可用，回退到逐个检查
                    templateFormulas = new Array(incomeArr[0].length);
                    for (let colIndex = 0; colIndex < incomeArr[0].length; colIndex++) {
                        const cellValue = incomeArr[templateRowIndex][colIndex];
                        if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('=')) {
                            templateFormulas[colIndex] = cellValue;
                        }
                    }
                }
            } catch (e) {
                console.warn('无法获取公式，使用备用方法');
                // 备用方法：从原始数据中检查公式
                templateFormulas = new Array(incomeArr[0].length);
                for (let colIndex = 0; colIndex < incomeArr[0].length; colIndex++) {
                    const cellValue = incomeArr[templateRowIndex][colIndex];
                    if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('=')) {
                        templateFormulas[colIndex] = cellValue;
                    }
                }
            }
        }

        console.log(`从第${templateRowIndex + 1}行获取到${templateFormulas.filter(f => f).length}个公式模板`);

        // 批量插入新行并准备数据
        const insertRowIndex = incomeSheet.getLastRow() + 1;
        const newRowsData: any[][] = [];
        const newRowsFormulas: string[][] = [];

        missingLocators.forEach((missingItem, index) => {
            const currentRowIndex = insertRowIndex + index;

            // 准备新行的数据
            const newRowData = new Array(incomeArr[0].length).fill('');
            const newRowFormulas = new Array(incomeArr[0].length).fill('');

            // 根据列名映射关系复制数据
            Object.keys(columnMapping).forEach(incomeColumnName => {
                const quickColumnName = columnMapping[incomeColumnName];

                if (incomeColumnIndexes.hasOwnProperty(incomeColumnName) &&
                    quickColumnIndexes.hasOwnProperty(quickColumnName)) {

                    const incomeColIndex = incomeColumnIndexes[incomeColumnName];
                    const quickColIndex = quickColumnIndexes[quickColumnName];
                    const value = missingItem.quickRowData[quickColIndex];

                    // 如果值不为空，则设置到新行数据中
                    if (value !== undefined && value !== null && value !== '') {
                        newRowData[incomeColIndex] = value;
                    }
                }
            });

            // 计算并设置公式
            for (let colIndex = 0; colIndex < templateFormulas.length; colIndex++) {
                const templateFormula = templateFormulas[colIndex];
                if (templateFormula && templateFormula.startsWith('=')) {
                    // 检查当前列是否已经有数据值，如果有则不设置公式
                    if (!newRowData[colIndex] || newRowData[colIndex] === '') {
                        // 调整公式中的行号引用
                        const adjustedFormula = adjustFormulaForNewRow(templateFormula, templateRowIndex, currentRowIndex);
                        newRowFormulas[colIndex] = adjustedFormula;
                    }
                }
            }

            newRowsData.push(newRowData);
            newRowsFormulas.push(newRowFormulas);
        });

        // 批量插入行
        for (let i = 0; i < missingLocators.length; i++) {
            incomeSheet.insertRowAfter(insertRowIndex + i - 1);
        }

        // 批量设置数据值（优化：使用setValues批量操作）
        if (newRowsData.length > 0) {
            const dataRange = incomeSheet.getRange(insertRowIndex, 0, newRowsData.length, newRowsData[0].length);
            dataRange.setValues(newRowsData);
        }

        // 批量设置公式（优化：只设置有公式的单元格）
        newRowsFormulas.forEach((rowFormulas, rowIndex) => {
            rowFormulas.forEach((formula, colIndex) => {
                if (formula && formula.startsWith('=')) {
                    const cell = incomeSheet.getRange(insertRowIndex + rowIndex, colIndex);
                    cell.setValue(formula);
                }
            });
        });

        console.log(`成功添加${missingLocators.length}行数据到收入成本测算sheet`);

    } catch (error) {
        console.error('利润中心补全时发生错误:', error);
        throw error;
    }
}

/**
 * 调整公式中的行号引用
 * @param formula 原始公式
 * @param fromRow 原始行号（0-based）
 * @param toRow 目标行号（0-based）
 * @returns 调整后的公式
 */
function adjustFormulaForNewRow(formula: string, fromRow: number, toRow: number): string {
    console.log(`调整公式: ${formula}, 从行${fromRow}到行${toRow}`);

    // 将0-based转换为1-based（Excel/Univer使用1-based行号）
    const fromRowExcel = fromRow + 1;
    const toRowExcel = toRow + 1;
    const rowDiff = toRowExcel - fromRowExcel;

    console.log(`行号差异: ${rowDiff} (从Excel行${fromRowExcel}到Excel行${toRowExcel})`);

    // 使用正则表达式匹配公式中的单元格引用（如A1, B2, $C$3等）
    const adjustedFormula = formula.replace(/([A-Z]+)(\$?)(\d+)/g, (match, col, dollarSign, row) => {
        if (dollarSign === '$') {
            // 绝对引用，不需要调整
            console.log(`保持绝对引用: ${match}`);
            return match;
        } else {
            // 相对引用，需要调整行号
            const oldRow = parseInt(row);
            const newRow = oldRow + rowDiff;
            const result = col + dollarSign + newRow;
            console.log(`调整相对引用: ${match} -> ${result}`);
            return result;
        }
    });

    console.log(`最终调整后的公式: ${adjustedFormula}`);
    return adjustedFormula;
}



